#!/usr/bin/env python3
"""
CubeAI Simple Resume Analyzer - Lightweight AI Backend
Rezi.ai-inspired resume analysis without heavy dependencies
Serves frontend/hr-dashboard.html

Author: Cube AI Solutions
Version: 1.0.0
"""

import os
import json
import re
from datetime import datetime
from typing import Dict, List, Optional
import logging

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS

# OpenAI Integration
try:
    import openai
    OPENAI_AVAILABLE = True
    print("✅ OpenAI library imported successfully")
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI library not found. Install with: pip install openai")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__, static_folder='../frontend', static_url_path='')

# Configuration
app.config['SECRET_KEY'] = 'cubeai-simple-analyzer-2024'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['FRONTEND_FOLDER'] = '../frontend'

# Configure CORS
CORS(app)

# Create upload directory
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize OpenAI
openai_client = None
if OPENAI_AVAILABLE:
    # OpenAI API key configuration
    openai_api_key = os.environ.get('OPENAI_API_KEY') or '********************************************************************************************************************************************************************'

    if openai_api_key:
        try:
            from openai import OpenAI
            openai_client = OpenAI(api_key=openai_api_key)
            # Test the connection
            test_response = openai_client.models.list()
            logger.info("✅ OpenAI API initialized successfully")
            logger.info("🤖 Enhanced AI analysis capabilities enabled")
            logger.info(f"🔗 Connected to OpenAI with {len(test_response.data)} models available")
        except Exception as e:
            error_msg = str(e)
            if "insufficient_quota" in error_msg or "quota" in error_msg.lower():
                logger.error("❌ OpenAI API quota exceeded. Please check your billing.")
                logger.warning("💳 Add credits to your OpenAI account to enable AI analysis")
            elif "invalid_api_key" in error_msg:
                logger.error("❌ Invalid OpenAI API key provided")
            else:
                logger.error(f"❌ Failed to initialize OpenAI: {error_msg}")
            logger.warning("🔄 Falling back to basic analysis mode")
            openai_client = None
    else:
        logger.warning("⚠️ OpenAI API key not found.")
else:
    logger.warning("⚠️ OpenAI not available. Install with: pip install openai")

class SimpleResumeAnalyzer:
    """
    Enhanced Resume Analyzer with OpenAI integration
    Rezi.ai-inspired with advanced AI capabilities
    """

    def __init__(self):
        self.openai_client = openai_client
        self.skills_keywords = {
            'technical': [
                'python', 'javascript', 'java', 'react', 'angular', 'vue', 'node.js',
                'sql', 'mongodb', 'aws', 'azure', 'docker', 'git', 'html', 'css'
            ],
            'data_science': [
                'machine learning', 'data analysis', 'pandas', 'numpy', 'tableau',
                'power bi', 'statistics', 'excel', 'r', 'tensorflow', 'pytorch'
            ],
            'soft_skills': [
                'leadership', 'communication', 'teamwork', 'problem solving',
                'project management', 'time management', 'adaptability'
            ],
            'business': [
                'strategy', 'marketing', 'sales', 'finance', 'operations',
                'business analysis', 'product management', 'customer service'
            ]
        }
        
        self.ats_keywords = [
            'experience', 'skills', 'education', 'certification', 'achievement',
            'responsibility', 'accomplishment', 'project', 'team', 'management'
        ]
    
    def extract_text_from_file(self, file_path: str) -> str:
        """Simple text extraction for demo purposes"""
        try:
            # For demo, we'll simulate text extraction
            # In production, you'd use PyMuPDF, pdfplumber, etc.
            with open(file_path, 'rb') as f:
                content = f.read()
                # Simple text extraction simulation
                if file_path.lower().endswith('.txt'):
                    return content.decode('utf-8', errors='ignore')
                else:
                    # Simulate extracted text for PDF/DOCX
                    return """
                    John Doe
                    Software Engineer
                    <EMAIL>
                    +1-555-0123
                    
                    EXPERIENCE
                    Senior Software Engineer - Tech Corp (2020-2023)
                    • Developed web applications using Python, JavaScript, and React
                    • Led a team of 5 developers on multiple projects
                    • Implemented CI/CD pipelines using Docker and AWS
                    • Improved application performance by 40%
                    
                    Software Developer - StartupXYZ (2018-2020)
                    • Built REST APIs using Node.js and MongoDB
                    • Collaborated with cross-functional teams
                    • Participated in agile development processes
                    
                    EDUCATION
                    Bachelor of Science in Computer Science
                    University of Technology (2014-2018)
                    
                    SKILLS
                    Programming: Python, JavaScript, Java, SQL
                    Frameworks: React, Angular, Node.js, Django
                    Tools: Git, Docker, AWS, Jenkins
                    Databases: MongoDB, PostgreSQL, MySQL
                    """
        except Exception as e:
            logger.error(f"Text extraction failed: {str(e)}")
            return ""
    
    def extract_contact_info(self, text: str) -> Dict[str, str]:
        """Extract contact information"""
        contact_info = {}
        
        # Email extraction
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        if emails:
            contact_info['email'] = emails[0]
        
        # Phone extraction
        phone_pattern = r'(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
        phones = re.findall(phone_pattern, text)
        if phones:
            contact_info['phone'] = ''.join(phones[0]) if isinstance(phones[0], tuple) else phones[0]
        
        return contact_info
    
    def extract_skills(self, text: str) -> Dict[str, List[str]]:
        """Extract skills from text"""
        text_lower = text.lower()
        found_skills = {category: [] for category in self.skills_keywords.keys()}
        
        for category, skills in self.skills_keywords.items():
            for skill in skills:
                if skill.lower() in text_lower:
                    found_skills[category].append(skill)
        
        return found_skills
    
    def calculate_ats_score(self, text: str) -> Dict[str, any]:
        """Calculate ATS compatibility score"""
        score = 0
        breakdown = {
            'formatting': 0,
            'keywords': 0,
            'structure': 0,
            'content': 0
        }
        
        # Check for keywords
        keyword_count = sum(1 for keyword in self.ats_keywords if keyword.lower() in text.lower())
        breakdown['keywords'] = min(25, (keyword_count / len(self.ats_keywords)) * 25)
        
        # Check for structure
        sections = ['experience', 'education', 'skills']
        section_count = sum(1 for section in sections if section in text.lower())
        breakdown['structure'] = (section_count / len(sections)) * 25
        
        # Check content length
        word_count = len(text.split())
        if 200 <= word_count <= 800:
            breakdown['content'] = 25
        elif word_count > 100:
            breakdown['content'] = 15
        
        # Check formatting (simple heuristics)
        if '@' in text and any(char.isdigit() for char in text):
            breakdown['formatting'] = 25
        
        total_score = sum(breakdown.values())
        
        return {
            'total_score': round(total_score, 1),
            'breakdown': breakdown,
            'grade': self._get_grade(total_score)
        }
    
    def _get_grade(self, score: float) -> str:
        """Convert score to grade"""
        if score >= 90: return 'A+'
        elif score >= 80: return 'A'
        elif score >= 70: return 'B+'
        elif score >= 60: return 'B'
        elif score >= 50: return 'C+'
        else: return 'C'
    
    def analyze_job_match(self, resume_text: str, job_description: str) -> Dict[str, any]:
        """Analyze job matching"""
        if not job_description.strip():
            return {
                'match_score': 0,
                'matched_skills': [],
                'missing_skills': [],
                'recommendations': ['Please provide a job description']
            }
        
        resume_skills = self.extract_skills(resume_text)
        job_skills = self.extract_skills(job_description)
        
        # Flatten skills
        resume_skills_flat = []
        job_skills_flat = []
        
        for skills_list in resume_skills.values():
            resume_skills_flat.extend(skills_list)
        
        for skills_list in job_skills.values():
            job_skills_flat.extend(skills_list)
        
        matched_skills = list(set(resume_skills_flat) & set(job_skills_flat))
        missing_skills = list(set(job_skills_flat) - set(resume_skills_flat))
        
        # Simple matching score
        match_score = (len(matched_skills) / max(len(job_skills_flat), 1)) * 100
        
        return {
            'match_score': round(match_score, 1),
            'matched_skills': matched_skills,
            'missing_skills': missing_skills[:10],
            'recommendations': self._generate_recommendations(match_score, missing_skills)
        }
    
    def _generate_recommendations(self, match_score: float, missing_skills: List[str]) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []
        
        if match_score < 50:
            recommendations.append("Consider highlighting more relevant experience")
            recommendations.append("Add quantifiable achievements to strengthen your profile")
        
        if missing_skills:
            recommendations.append(f"Consider adding these skills: {', '.join(missing_skills[:5])}")
        
        if match_score >= 80:
            recommendations.append("Great match! Consider customizing your summary")
        
        return recommendations

    # OpenAI-Powered Analysis Methods
    def analyze_with_openai(self, resume_text: str, job_description: str = "") -> Dict[str, any]:
        """
        Enhanced analysis using OpenAI GPT for more sophisticated insights
        Similar to Rezi.ai's AI-powered analysis
        """
        if not self.openai_client:
            logger.warning("OpenAI not available, using fallback analysis")
            return self._fallback_analysis(resume_text, job_description)

        try:
            # Create comprehensive analysis prompt
            analysis_prompt = self._create_analysis_prompt(resume_text, job_description)

            # Call OpenAI API
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert HR analyst and resume optimization specialist, similar to Rezi.ai. Provide detailed, actionable insights for resume improvement."},
                    {"role": "user", "content": analysis_prompt}
                ],
                max_tokens=1500,
                temperature=0.3
            )

            # Parse OpenAI response
            ai_analysis = response.choices[0].message.content
            return self._parse_openai_response(ai_analysis, resume_text, job_description)

        except Exception as e:
            error_msg = str(e)
            if "insufficient_quota" in error_msg or "quota" in error_msg.lower():
                logger.error("💳 OpenAI quota exceeded - using enhanced fallback analysis")
                return self._enhanced_fallback_analysis(resume_text, job_description, "quota_exceeded")
            elif "invalid_api_key" in error_msg:
                logger.error("🔑 Invalid OpenAI API key - using basic analysis")
                return self._enhanced_fallback_analysis(resume_text, job_description, "invalid_key")
            else:
                logger.error(f"OpenAI analysis failed: {error_msg}")
                return self._enhanced_fallback_analysis(resume_text, job_description, "api_error")

    def _create_analysis_prompt(self, resume_text: str, job_description: str) -> str:
        """Create comprehensive prompt for OpenAI analysis"""
        prompt = f"""
        Analyze this resume and provide detailed insights like Rezi.ai would:

        RESUME TEXT:
        {resume_text[:3000]}  # Limit text to avoid token limits

        JOB DESCRIPTION:
        {job_description[:1000] if job_description else "No specific job description provided"}

        Please provide analysis in this JSON format:
        {{
            "overall_score": <score 0-100>,
            "ats_compatibility": {{
                "score": <score 0-100>,
                "issues": ["list of ATS issues"],
                "improvements": ["list of improvements"]
            }},
            "skills_analysis": {{
                "found_skills": ["list of skills found"],
                "missing_skills": ["skills missing for the job"],
                "skill_strength": "weak/moderate/strong"
            }},
            "content_quality": {{
                "score": <score 0-100>,
                "strengths": ["list of strengths"],
                "weaknesses": ["list of weaknesses"]
            }},
            "job_match": {{
                "match_percentage": <percentage 0-100>,
                "matched_requirements": ["requirements that match"],
                "missing_requirements": ["requirements not met"]
            }},
            "optimization_suggestions": [
                "specific actionable suggestions for improvement"
            ],
            "summary": "Brief overall assessment and next steps"
        }}
        """
        return prompt

    def _parse_openai_response(self, ai_response: str, resume_text: str, job_description: str) -> Dict[str, any]:
        """Parse OpenAI response and structure the results"""
        try:
            # Try to extract JSON from the response
            import json

            # Find JSON in the response
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = ai_response[start_idx:end_idx]
                ai_analysis = json.loads(json_str)

                # Combine with basic analysis
                basic_analysis = {
                    'contact_info': self.extract_contact_info(resume_text),
                    'skills': self.extract_skills(resume_text),
                    'ats_score': self.calculate_ats_score(resume_text),
                    'job_match': self.analyze_job_match(resume_text, job_description)
                }

                # Enhanced analysis with OpenAI insights
                enhanced_analysis = {
                    **basic_analysis,
                    'ai_insights': ai_analysis,
                    'openai_powered': True,
                    'overall_assessment': ai_analysis.get('summary', 'AI analysis completed'),
                    'optimization_suggestions': ai_analysis.get('optimization_suggestions', [])
                }

                return enhanced_analysis
            else:
                raise ValueError("No valid JSON found in OpenAI response")

        except Exception as e:
            logger.error(f"Failed to parse OpenAI response: {str(e)}")
            return self._fallback_analysis(resume_text, job_description)

    def _fallback_analysis(self, resume_text: str, job_description: str) -> Dict[str, any]:
        """Fallback analysis when OpenAI is not available"""
        return self._enhanced_fallback_analysis(resume_text, job_description, "openai_unavailable")

    def _enhanced_fallback_analysis(self, resume_text: str, job_description: str, reason: str) -> Dict[str, any]:
        """Enhanced fallback analysis with detailed insights"""
        contact_info = self.extract_contact_info(resume_text)
        skills = self.extract_skills(resume_text)
        ats_score = self.calculate_ats_score(resume_text)
        job_match = self.analyze_job_match(resume_text, job_description)

        # Enhanced analysis messages based on reason
        status_messages = {
            "quota_exceeded": "⚠️ OpenAI quota exceeded. Using enhanced local analysis. Add credits to enable AI insights.",
            "invalid_key": "🔑 Invalid OpenAI API key. Using enhanced local analysis. Check your API key configuration.",
            "api_error": "🔧 OpenAI API error. Using enhanced local analysis. Service will retry automatically.",
            "openai_unavailable": "📊 Using enhanced local analysis. OpenAI integration not configured."
        }

        # Generate enhanced recommendations
        enhanced_recommendations = self._generate_enhanced_recommendations(
            ats_score, job_match, skills, contact_info, resume_text
        )

        return {
            'contact_info': contact_info,
            'skills': skills,
            'ats_score': ats_score,
            'job_match': job_match,
            'openai_powered': False,
            'analysis_status': status_messages.get(reason, "Using local analysis"),
            'overall_assessment': f'Enhanced local analysis completed. {status_messages.get(reason, "")}',
            'optimization_suggestions': enhanced_recommendations,
            'ai_insights': {
                'overall_score': ats_score['total_score'],
                'content_quality': {
                    'score': ats_score['total_score'],
                    'strengths': self._identify_strengths(resume_text, skills),
                    'weaknesses': self._identify_weaknesses(ats_score)
                },
                'summary': f"Resume analysis completed using local algorithms. Score: {ats_score['total_score']}%"
            }
        }

    def _generate_enhanced_recommendations(self, ats_score, job_match, skills, contact_info, resume_text):
        """Generate enhanced recommendations based on analysis"""
        recommendations = []

        # ATS Score recommendations
        if ats_score['total_score'] < 70:
            recommendations.append("🔧 Improve ATS compatibility by using standard section headers")
            recommendations.append("📝 Add more relevant keywords from the job description")

        # Skills recommendations
        if job_match['missing_skills']:
            recommendations.append(f"🎯 Consider adding these skills: {', '.join(job_match['missing_skills'][:3])}")

        # Contact info recommendations
        if not contact_info.get('email'):
            recommendations.append("📧 Add a professional email address")
        if not contact_info.get('phone'):
            recommendations.append("📱 Include a phone number for contact")

        # Content recommendations
        if len(resume_text.split()) < 200:
            recommendations.append("📄 Consider expanding your resume with more detailed experience")
        elif len(resume_text.split()) > 800:
            recommendations.append("✂️ Consider condensing your resume for better readability")

        return recommendations[:8]  # Limit to 8 recommendations

    def _identify_strengths(self, resume_text, skills):
        """Identify resume strengths"""
        strengths = []

        total_skills = sum(len(skill_list) for skill_list in skills.values())
        if total_skills > 10:
            strengths.append("Strong technical skill set")

        if any(keyword in resume_text.lower() for keyword in ['experience', 'years', 'project']):
            strengths.append("Clear experience documentation")

        if any(keyword in resume_text.lower() for keyword in ['led', 'managed', 'developed', 'created']):
            strengths.append("Action-oriented language")

        return strengths[:5]

    def _identify_weaknesses(self, ats_score):
        """Identify areas for improvement"""
        weaknesses = []

        if ats_score['breakdown'].get('formatting', 0) < 80:
            weaknesses.append("Formatting could be improved")

        if ats_score['breakdown'].get('keywords', 0) < 70:
            weaknesses.append("More relevant keywords needed")

        if ats_score['breakdown'].get('structure', 0) < 75:
            weaknesses.append("Resume structure needs enhancement")

        return weaknesses[:5]

# Initialize analyzer
analyzer = SimpleResumeAnalyzer()

# API Routes
@app.route('/api/analyze-resume', methods=['POST'])
def analyze_resume():
    """Main resume analysis endpoint"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400
        
        file = request.files['file']
        job_description = request.form.get('job_description', '')
        
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Save file
        filename = file.filename
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # Extract text
        text = analyzer.extract_text_from_file(file_path)
        
        if not text.strip():
            return jsonify({'error': 'Could not extract text from file'}), 400
        
        # Perform enhanced analysis with OpenAI
        if analyzer.openai_client:
            logger.info("🤖 Using OpenAI-powered analysis")
            analysis_result = analyzer.analyze_with_openai(text, job_description)
        else:
            logger.info("📊 Using basic analysis (OpenAI not available)")
            analysis_result = {
                'contact_info': analyzer.extract_contact_info(text),
                'skills': analyzer.extract_skills(text),
                'ats_score': analyzer.calculate_ats_score(text),
                'job_match': analyzer.analyze_job_match(text, job_description),
                'openai_powered': False
            }

        # Add metadata
        analysis_result.update({
            'text_length': len(text),
            'word_count': len(text.split()),
            'analysis_type': 'OpenAI Enhanced' if analyzer.openai_client else 'Basic Analysis'
        })

        # Clean up file
        os.remove(file_path)

        return jsonify({
            'success': True,
            'filename': filename,
            'analysis': analysis_result,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        return jsonify({'error': 'Analysis failed'}), 500

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'CubeAI Enhanced Resume Analyzer with OpenAI',
        'version': '2.0.0',
        'ai_capabilities': {
            'text_extraction': True,
            'skills_analysis': True,
            'ats_scoring': True,
            'job_matching': True,
            'contact_extraction': True,
            'openai_powered': analyzer.openai_client is not None,
            'advanced_insights': analyzer.openai_client is not None
        },
        'openai_status': {
            'available': OPENAI_AVAILABLE,
            'configured': analyzer.openai_client is not None,
            'model': 'gpt-3.5-turbo' if analyzer.openai_client else 'Not configured'
        },
        'endpoints': [
            '/api/analyze-resume',
            '/api/health'
        ],
        'analysis_type': 'OpenAI Enhanced' if analyzer.openai_client else 'Basic Analysis',
        'timestamp': datetime.now().isoformat()
    })

# Frontend routes
@app.route('/')
def serve_frontend():
    """Serve HR dashboard"""
    return send_from_directory(app.config['FRONTEND_FOLDER'], 'hr-dashboard.html')

@app.route('/<path:filename>')
def serve_static_files(filename):
    """Serve static files"""
    try:
        return send_from_directory(app.config['FRONTEND_FOLDER'], filename)
    except:
        return send_from_directory(app.config['FRONTEND_FOLDER'], 'hr-dashboard.html')

if __name__ == '__main__':
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    CUBE AI SOLUTIONS                         ║
    ║            Simple AI Resume Analyzer Backend                 ║
    ║          Lightweight Rezi.ai-inspired Engine                ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    print("🚀 Starting Simple AI Resume Analyzer...")
    print("✅ Lightweight backend ready!")
    print("🌐 Access: http://localhost:5000")
    print("📊 API: http://localhost:5000/api/analyze-resume")
    print("🔍 Health: http://localhost:5000/api/health")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
