<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CubeAI HR AI Automation Agent - Intelligent Recruitment System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="analyzer-enhanced.css" rel="stylesheet">
    <style>
        .hr-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .header-stat {
            text-align: center;
            padding: 0.5rem;
        }
        .connection-panel {
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .connection-panel h6 {
            color: white;
            margin-bottom: 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .ai-score {
            font-size: 2rem;
            font-weight: bold;
        }
        .score-excellent { color: #28a745; }
        .score-good { color: #17a2b8; }
        .score-average { color: #ffc107; }
        .score-poor { color: #dc3545; }
        .candidate-card {
            border-left: 4px solid #667eea;
            margin-bottom: 1rem;
        }
        .job-requirements {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
        }
        .skill-match {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        .skill-matched { background: #d4edda; color: #155724; }
        .skill-missing { background: #f8d7da; color: #721c24; }

        /* Chat Interface Styles Removed */

        /* Job Description Styles */
        .job-description-content {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Google Drive Integration Styles */
        .integration-widget .status-indicator {
            display: flex;
            align-items: center;
        }

        .stat-mini {
            text-align: center;
            padding: 0.5rem;
        }

        .stat-mini h6 {
            font-size: 1.2rem;
            font-weight: bold;
            color: #495057;
        }
        .bulk-upload-area {
            border: 2px dashed #667eea;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .bulk-upload-area:hover {
            background: #e9ecef;
            border-color: #5a6fd8;
        }
        .progress-container {
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <!-- Enhanced HR Header -->
    <header class="hr-header">
        <div class="container">
            <!-- Main Header Row -->
            <div class="row align-items-center mb-3">
                <div class="col-md-6">
                    <h1 class="display-5 mb-2">
                        <i class="bi bi-robot me-3"></i>
                        CubeAI HR AI Automation Agent
                    </h1>
                    <p class="lead mb-0">Intelligent Recruitment Automation System</p>
                </div>
                <div class="col-md-6">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="header-stat">
                                <i class="bi bi-database fs-4 mb-1"></i>
                                <div><small>Database</small></div>
                                <span id="dbStatus" class="badge bg-warning">Not Connected</span>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="header-stat">
                                <i class="bi bi-google fs-4 mb-1"></i>
                                <div><small>Google Drive</small></div>
                                <span id="driveStatus" class="badge bg-warning">Not Connected</span>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="header-stat">
                                <i class="bi bi-file-earmark-person fs-4 mb-1"></i>
                                <div><small>Total CVs</small></div>
                                <span id="totalCVs" class="badge bg-info">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Connection Management Row -->
            <div class="row">
                <div class="col-12">
                    <div class="connection-panel bg-white bg-opacity-10 rounded p-3">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="mb-2"><i class="bi bi-link-45deg me-2"></i>Quick Connections</h6>
                            </div>
                            <div class="col-md-9">
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-light btn-sm w-100" onclick="connectDatabase()">
                                            <i class="bi bi-database me-1"></i>Database
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-light btn-sm w-100" onclick="connectGoogleDrive()">
                                            <i class="bi bi-google me-1"></i>Google Drive
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-light btn-sm w-100" onclick="manageCVs()">
                                            <i class="bi bi-file-earmark-person me-1"></i>CV Management
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-light btn-sm w-100" onclick="startAIAnalysis()">
                                            <i class="bi bi-cpu me-1"></i>AI Analysis
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="container my-5">
        <!-- Navigation Tabs -->
        <ul class="nav nav-pills nav-fill mb-4" id="hrTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="database-connection-tab" data-bs-toggle="pill" data-bs-target="#database-connection" type="button" role="tab">
                    <i class="bi bi-database me-2"></i>Database Connection
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="google-drive-tab" data-bs-toggle="pill" data-bs-target="#google-drive" type="button" role="tab">
                    <i class="bi bi-google me-2"></i>Google Drive
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="cv-management-tab" data-bs-toggle="pill" data-bs-target="#cv-management" type="button" role="tab">
                    <i class="bi bi-file-earmark-person me-2"></i>CV Management
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="job-creation-tab" data-bs-toggle="pill" data-bs-target="#job-creation" type="button" role="tab">
                    <i class="bi bi-briefcase me-2"></i>Job Creation
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="ai-analysis-tab" data-bs-toggle="pill" data-bs-target="#ai-analysis" type="button" role="tab">
                    <i class="bi bi-cpu me-2"></i>AI Analysis
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="ai-reports-tab" data-bs-toggle="pill" data-bs-target="#ai-reports" type="button" role="tab">
                    <i class="bi bi-robot me-2"></i>AI Reports
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="hrTabContent">

            <!-- Database Connection Tab -->
            <div class="tab-pane fade show active" id="database-connection" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-database me-2"></i>
                                    External Database Connection
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- Database Type Selection -->
                                    <div class="col-md-6">
                                        <div class="card h-100">
                                            <div class="card-header">
                                                <h6><i class="bi bi-gear me-2"></i>Database Configuration</h6>
                                            </div>
                                            <div class="card-body">
                                                <form id="databaseConfigForm">
                                                    <div class="mb-3">
                                                        <label class="form-label">Database Type</label>
                                                        <select class="form-select" id="dbType" required>
                                                            <option value="">Select Database Type</option>
                                                            <option value="mysql">MySQL</option>
                                                            <option value="postgresql">PostgreSQL</option>
                                                            <option value="mongodb">MongoDB</option>
                                                            <option value="oracle">Oracle Database</option>
                                                            <option value="sqlserver">SQL Server</option>
                                                            <option value="sqlite">SQLite</option>

                                                        </select>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label class="form-label">Host/Server</label>
                                                        <input type="text" class="form-control" id="dbHost" placeholder="localhost or server IP">
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Port</label>
                                                                <input type="number" class="form-control" id="dbPort" placeholder="3306">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Database Name</label>
                                                                <input type="text" class="form-control" id="dbName" placeholder="hr_database">
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Username</label>
                                                                <input type="text" class="form-control" id="dbUsername" placeholder="database_user">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Password</label>
                                                                <input type="password" class="form-control" id="dbPassword" placeholder="password">
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="d-grid gap-2">
                                                        <button type="button" class="btn btn-primary" onclick="hrDashboard.testDatabaseConnection()">
                                                            <i class="bi bi-wifi me-2"></i>Test Connection
                                                        </button>
                                                        <button type="button" class="btn btn-success" onclick="hrDashboard.connectToDatabase()">
                                                            <i class="bi bi-database-check me-2"></i>Connect to Database
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Connection Status & AI Agent Info -->
                                    <div class="col-md-6">
                                        <div class="card h-100">
                                            <div class="card-header">
                                                <h6><i class="bi bi-robot me-2"></i>AI Agent Status</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-4">
                                                    <h6>Connection Status</h6>
                                                    <div id="connectionStatus" class="alert alert-info">
                                                        <i class="bi bi-info-circle me-2"></i>
                                                        Not connected to external database
                                                    </div>
                                                </div>

                                                <div class="mb-4">
                                                    <h6>AI Agent Capabilities</h6>
                                                    <ul class="list-unstyled">
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>Automated CV Parsing</li>
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>Intelligent Job Matching</li>
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>AI-Powered Scoring</li>
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>Smart Recommendations</li>
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>Automated Reporting</li>
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>Real-time Analytics</li>
                                                    </ul>
                                                </div>

                                                <div class="mb-3">
                                                    <h6>Database Tables</h6>
                                                    <div id="databaseTables" class="small text-muted">
                                                        Connect to database to view available tables
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                    <h6>Data Sync Options</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="autoSync" checked>
                                                        <label class="form-check-label" for="autoSync">
                                                            Auto-sync with external database
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="realTimeAnalysis" checked>
                                                        <label class="form-check-label" for="realTimeAnalysis">
                                                            Real-time AI analysis
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Actions -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6><i class="bi bi-lightning me-2"></i>Quick Actions</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="hrDashboard.syncCandidates()">
                                                            <i class="bi bi-arrow-repeat me-2"></i>Sync Candidates
                                                        </button>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <button class="btn btn-outline-success w-100 mb-2" onclick="hrDashboard.syncJobs()">
                                                            <i class="bi bi-briefcase me-2"></i>Sync Jobs
                                                        </button>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <button class="btn btn-outline-info w-100 mb-2" onclick="hrDashboard.runAIAnalysis()">
                                                            <i class="bi bi-cpu me-2"></i>Run AI Analysis
                                                        </button>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <button class="btn btn-outline-warning w-100 mb-2" onclick="hrDashboard.generateAIReport()">
                                                            <i class="bi bi-file-earmark-text me-2"></i>Generate Report
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Drive Connection Tab -->
            <div class="tab-pane fade" id="google-drive" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header text-white" style="background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);">
                                <h5 class="mb-0">
                                    <i class="bi bi-google me-2"></i>
                                    Google Drive Integration
                                </h5>
                                <small>Connect to Google Drive for automated CV management</small>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- Google Drive Configuration -->
                                    <div class="col-md-6">
                                        <div class="card h-100">
                                            <div class="card-header bg-info text-white">
                                                <h6><i class="bi bi-gear me-2"></i>Google Drive Configuration</h6>
                                            </div>
                                            <div class="card-body">
                                                <form id="googleDriveConfigForm">
                                                    <div class="mb-3">
                                                        <label class="form-label">Authentication Method</label>
                                                        <select class="form-select" id="authMethod" required>
                                                            <option value="">Select Authentication Method</option>
                                                            <option value="oauth">OAuth 2.0 (Recommended)</option>
                                                            <option value="service_account">Service Account</option>
                                                            <option value="api_key">API Key</option>
                                                        </select>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label class="form-label">Google Drive Folder ID</label>
                                                        <input type="text" class="form-control" id="driveFolderId" placeholder="Enter Google Drive folder ID">
                                                        <div class="form-text">
                                                            <i class="bi bi-info-circle me-1"></i>
                                                            Copy folder ID from Google Drive URL
                                                        </div>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label class="form-label">Credentials File</label>
                                                        <input type="file" class="form-control" id="credentialsFile" accept=".json">
                                                        <div class="form-text">Upload Google API credentials JSON file</div>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label class="form-label">Sync Settings</label>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="autoSync" checked>
                                                            <label class="form-check-label" for="autoSync">
                                                                Auto-sync every hour
                                                            </label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="realTimeSync">
                                                            <label class="form-check-label" for="realTimeSync">
                                                                Real-time sync (webhook)
                                                            </label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="autoAIAnalysisGD" checked>
                                                            <label class="form-check-label" for="autoAIAnalysisGD">
                                                                Auto AI Analysis on new CVs
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div class="d-grid gap-2">
                                                        <button type="button" class="btn btn-primary" onclick="connectGoogleDrive()">
                                                            <i class="bi bi-google me-2"></i>Connect to Google Drive
                                                        </button>
                                                        <button type="button" class="btn btn-outline-secondary" onclick="testGoogleDriveConnection()">
                                                            <i class="bi bi-check-circle me-2"></i>Test Connection
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Google Drive Status & Management -->
                                    <div class="col-md-6">
                                        <div class="card h-100">
                                            <div class="card-header bg-success text-white">
                                                <h6><i class="bi bi-cloud-check me-2"></i>Connection Status & Management</h6>
                                            </div>
                                            <div class="card-body">
                                                <!-- Connection Status -->
                                                <div class="mb-4">
                                                    <h6>Connection Status</h6>
                                                    <div id="googleDriveStatus" class="alert alert-warning">
                                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                                        <strong>Not Connected</strong><br>
                                                        <small>Please contact admin to configure Google Drive connection</small>
                                                    </div>
                                                </div>

                                                <!-- Drive Statistics -->
                                                <div class="mb-4">
                                                    <h6>Drive Statistics</h6>
                                                    <div class="row text-center">
                                                        <div class="col-4">
                                                            <div class="stat-mini">
                                                                <h6 id="totalDriveFiles">0</h6>
                                                                <small class="text-muted">Total Files</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="stat-mini">
                                                                <h6 id="newDriveFiles">0</h6>
                                                                <small class="text-muted">New Files</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="stat-mini">
                                                                <h6 id="lastSyncTime">Never</h6>
                                                                <small class="text-muted">Last Sync</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Quick Actions -->
                                                <div class="d-grid gap-2">
                                                    <button class="btn btn-success" onclick="syncFromGoogleDrive()">
                                                        <i class="bi bi-arrow-clockwise me-2"></i>Sync Now
                                                    </button>
                                                    <button class="btn btn-outline-primary" onclick="viewGoogleDriveFiles()">
                                                        <i class="bi bi-folder me-2"></i>View Drive Files
                                                    </button>
                                                    <button class="btn btn-outline-info" onclick="runGoogleDriveAIAnalysis()">
                                                        <i class="bi bi-cpu me-2"></i>Run AI Analysis on Drive Files
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="disconnectGoogleDrive()">
                                                        <i class="bi bi-x-circle me-2"></i>Disconnect
                                                    </button>
                                                </div>

                                                <div class="mt-3">
                                                    <small class="text-muted">
                                                        <i class="bi bi-shield-check me-1"></i>
                                                        All data is processed securely and in compliance with privacy policies
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Google Drive File Browser -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="bi bi-folder me-2"></i>Google Drive Files
                                                </h6>
                                                <div>
                                                    <button class="btn btn-light btn-sm me-2" onclick="refreshGoogleDriveFiles()">
                                                        <i class="bi bi-arrow-clockwise"></i> Refresh
                                                    </button>
                                                    <button class="btn btn-success btn-sm" onclick="bulkDownloadFromDrive()">
                                                        <i class="bi bi-download"></i> Bulk Download
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div id="googleDriveFilesList">
                                                    <div class="text-center p-4">
                                                        <i class="bi bi-google display-4 text-muted mb-3"></i>
                                                        <h6>Connect to Google Drive to view files</h6>
                                                        <p class="text-muted">Configure your Google Drive connection above to start managing CV files</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- CV Management Tab -->
            <div class="tab-pane fade" id="cv-management" role="tabpanel">
                <div class="row">
                    <!-- Bulk CV Upload -->
                    <div class="col-md-6">
                        <div class="card feature-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-cloud-upload me-2"></i>
                                    Bulk CV Upload
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="bulk-upload-area" id="bulkUploadArea">
                                    <i class="bi bi-cloud-upload display-4 text-muted mb-3"></i>
                                    <h6>Drag & Drop Multiple CVs Here</h6>
                                    <p class="text-muted">Or click to select files</p>
                                    <input type="file" id="bulkFileInput" multiple accept=".pdf,.doc,.docx,.txt" style="display: none;">
                                    <button class="btn btn-outline-primary" onclick="document.getElementById('bulkFileInput').click()">
                                        Select Files
                                    </button>
                                </div>
                                <div id="uploadProgress" class="progress-container" style="display: none;">
                                    <div class="progress">
                                        <div id="uploadProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"></div>
                                    </div>
                                    <div id="uploadStatus" class="mt-2 text-center"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI Database Sync -->
                    <div class="col-md-6">
                        <div class="card feature-card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-robot me-2"></i>
                                    AI Database Sync
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-4">
                                    <h6><i class="bi bi-database-check me-2"></i>External Database Status</h6>
                                    <div id="externalDbStatus" class="alert alert-warning">
                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                        Connect to external database first
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h6><i class="bi bi-arrow-repeat me-2"></i>Sync Options</h6>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="syncCandidates" checked>
                                        <label class="form-check-label" for="syncCandidates">
                                            Sync Candidate Data
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="syncJobs" checked>
                                        <label class="form-check-label" for="syncJobs">
                                            Sync Job Postings
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="autoAIAnalysis" checked>
                                        <label class="form-check-label" for="autoAIAnalysis">
                                            Auto AI Analysis on Sync
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="hrDashboard.syncFromExternalDB()">
                                        <i class="bi bi-download me-2"></i>Sync from External DB
                                    </button>
                                    <button class="btn btn-success" onclick="hrDashboard.pushToExternalDB()">
                                        <i class="bi bi-upload me-2"></i>Push to External DB
                                    </button>
                                    <button class="btn btn-info" onclick="hrDashboard.runFullAIAnalysis()">
                                        <i class="bi bi-cpu me-2"></i>Run Full AI Analysis
                                    </button>
                                </div>

                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        AI Agent will automatically process and analyze all synced data
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CV Database View -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-database me-2"></i>
                                    CV Database
                                </h5>
                                <div>
                                    <button class="btn btn-light btn-sm me-2" id="refreshCVs">
                                        <i class="bi bi-arrow-clockwise"></i> Refresh
                                    </button>
                                    <button class="btn btn-warning btn-sm" id="exportCVs">
                                        <i class="bi bi-download"></i> Export
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" id="cvSearchInput" placeholder="Search candidates...">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="experienceFilter">
                                            <option value="">All Experience Levels</option>
                                            <option value="0-2">0-2 years</option>
                                            <option value="3-5">3-5 years</option>
                                            <option value="6-10">6-10 years</option>
                                            <option value="10+">10+ years</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="skillsFilter">
                                            <option value="">All Skills</option>
                                            <option value="JavaScript">JavaScript</option>
                                            <option value="Python">Python</option>
                                            <option value="React">React</option>
                                            <option value="Node.js">Node.js</option>
                                            <option value="Java">Java</option>
                                        </select>
                                    </div>
                                </div>
                                <div id="cvDatabaseList">
                                    <!-- CV list will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Job Creation Tab -->
            <div class="tab-pane fade" id="job-creation" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card feature-card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="bi bi-plus-square me-2"></i>
                                    Create New Job Role
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="jobCreationForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Job Title</label>
                                                <input type="text" class="form-control" id="jobTitle" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Department</label>
                                                <select class="form-select" id="jobDepartment" required>
                                                    <option value="">Select Department</option>
                                                    <option value="Engineering">Engineering</option>
                                                    <option value="Data Science">Data Science</option>
                                                    <option value="Product">Product</option>
                                                    <option value="Design">Design</option>
                                                    <option value="Marketing">Marketing</option>
                                                    <option value="Sales">Sales</option>
                                                    <option value="HR">Human Resources</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Experience Required</label>
                                                <select class="form-select" id="jobExperience" required>
                                                    <option value="">Select Experience</option>
                                                    <option value="0-1">0-1 years (Fresher)</option>
                                                    <option value="1-3">1-3 years (Junior)</option>
                                                    <option value="3-5">3-5 years (Mid-level)</option>
                                                    <option value="5-8">5-8 years (Senior)</option>
                                                    <option value="8+">8+ years (Lead/Principal)</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Job Type</label>
                                                <select class="form-select" id="jobType" required>
                                                    <option value="">Select Type</option>
                                                    <option value="Full-time">Full-time</option>
                                                    <option value="Part-time">Part-time</option>
                                                    <option value="Contract">Contract</option>
                                                    <option value="Internship">Internship</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Location</label>
                                                <input type="text" class="form-control" id="jobLocation" placeholder="Chennai, Tamil Nadu" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Required Skills</label>
                                        <textarea class="form-control" id="jobSkills" rows="2" placeholder="JavaScript, React, Node.js, MongoDB, AWS" required></textarea>
                                        <div class="form-text">Enter skills separated by commas</div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Job Description</label>
                                        <textarea class="form-control" id="jobDescription" rows="8" required placeholder="Enter detailed job description including responsibilities, requirements, and qualifications..."></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">AI Keywords (for better matching)</label>
                                        <textarea class="form-control" id="jobATSKeywords" rows="2" placeholder="software engineer, full stack, web development, agile, scrum"></textarea>
                                        <div class="form-text">Keywords that AI should prioritize when scoring candidates</div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="bi bi-plus-circle me-2"></i>Create Job Role
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary w-100" id="cancelEditBtn" style="display: none;" onclick="hrDashboard.cancelJobEdit()">
                                            <i class="bi bi-x-circle me-2"></i>Cancel Edit
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card feature-card">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-list-ul me-2"></i>
                                    Existing Jobs
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="existingJobsList">
                                    <!-- Existing jobs will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Analysis Tab -->
            <div class="tab-pane fade" id="ai-analysis" role="tabpanel">
                <div class="row">
                    <!-- AI Analysis Section -->
                    <div class="col-12 mb-4">
                        <div class="card feature-card border-primary">
                            <div class="card-header text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <h5 class="mb-0">
                                    <i class="bi bi-robot me-2"></i>
                                    AI-Powered Resume Analysis
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Job Description Input -->
                                <div class="mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="bi bi-file-text me-2 text-primary"></i>Job Requirements
                                    </label>
                                    <textarea class="form-control" id="customJobDescription" rows="6" placeholder="Describe the job requirements in natural language...

Example: Looking for a Senior Full Stack Developer with 5+ years experience in React, Node.js, and cloud platforms. Strong problem-solving skills required."></textarea>

                                </div>





                                <!-- File Upload Section -->
                                <div class="mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="bi bi-cloud-upload me-2 text-info"></i>Upload Resume for Analysis
                                    </label>
                                    <input type="file" class="form-control" id="resumeFileInput" accept=".pdf,.doc,.docx" multiple>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Supports PDF, DOC, DOCX files. Multiple files can be selected for batch analysis.
                                    </div>
                                </div>



                                <!-- Action Buttons -->
                                <div class="row g-2">
                                    <div class="col-md-4">
                                        <button class="btn btn-success btn-lg w-100" id="startAIAnalysisBtn" onclick="startAIAnalysis()">
                                            <i class="bi bi-cpu me-2"></i>Start AI Analysis
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-primary btn-lg w-100" id="runBulkAIAnalysis" onclick="runBulkAIAnalysis()">
                                            <i class="bi bi-lightning me-2"></i>Run AI Analysis
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-primary btn-lg w-100" id="loadFromGoogleDrive" onclick="loadFromGoogleDrive()">
                                            <i class="bi bi-google me-2"></i>Load from Google Drive
                                        </button>
                                    </div>
                                </div>

                                <!-- Bulk Analysis Info -->
                                <div class="alert alert-info mt-3" id="bulkAnalysisInfo">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>AI Automation:</strong> Connect your Google Drive or Database, enter job requirements, and click "Run AI Analysis" to automatically analyze all candidates and generate reports.
                                </div>

                                <!-- Analysis Progress -->
                                <div id="analysisProgress" class="mt-4" style="display: none;">
                                    <div class="d-flex align-items-center mb-2">
                                        <strong>AI Analysis in Progress...</strong>
                                        <div class="spinner-border spinner-border-sm ms-2" role="status"></div>
                                    </div>
                                    <div class="progress">
                                        <div id="analysisProgressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <div id="analysisStatus" class="mt-2 text-muted"></div>
                                </div>


                            </div>
                        </div>
                    </div>


                </div>

                <!-- Connected Candidates Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-people me-2"></i>
                                    Connected Candidates
                                </h5>
                                <div>
                                    <span class="badge bg-light text-dark me-2" id="totalCandidatesCount">0 Candidates</span>
                                    <button class="btn btn-light btn-sm" onclick="refreshCandidates()">
                                        <i class="bi bi-arrow-clockwise"></i> Refresh
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="candidatesStatus" class="mb-3">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <div class="stat-card bg-primary text-white p-3 rounded">
                                                <h4 id="dbCandidatesCount">0</h4>
                                                <small>Database</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="stat-card bg-success text-white p-3 rounded">
                                                <h4 id="driveCandidatesCount">0</h4>
                                                <small>Google Drive</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="stat-card bg-warning text-white p-3 rounded">
                                                <h4 id="analyzedCount">0</h4>
                                                <small>Analyzed</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="stat-card bg-info text-white p-3 rounded">
                                                <h4 id="matchingCount">0</h4>
                                                <small>Matching</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="candidatesList">
                                    <div class="text-center p-4">
                                        <i class="bi bi-person-plus display-4 text-muted mb-3"></i>
                                        <h6>No candidates connected</h6>
                                        <p class="text-muted">Connect your Google Drive or Database to see candidates here</p>
                                        <div class="mt-3">
                                            <button class="btn btn-primary me-2" onclick="document.getElementById('google-drive-tab').click()">
                                                <i class="bi bi-google me-1"></i>Connect Google Drive
                                            </button>
                                            <button class="btn btn-outline-primary" onclick="document.getElementById('database-connection-tab').click()">
                                                <i class="bi bi-database me-1"></i>Connect Database
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CV Data View Section -->
                <div class="row">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-file-earmark-person me-2"></i>
                                    CV Data View & Analysis
                                </h5>
                                <div>
                                    <button class="btn btn-light btn-sm me-2" id="refreshAnalysisCVs">
                                        <i class="bi bi-arrow-clockwise"></i> Refresh
                                    </button>
                                    <button class="btn btn-success btn-sm me-2" id="syncFromGoogleDriveAnalysis">
                                        <i class="bi bi-google"></i> Sync from Google Drive
                                    </button>
                                    <button class="btn btn-warning btn-sm" id="exportAnalysisResults">
                                        <i class="bi bi-download"></i> Export Results
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Search and Filter Controls -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" id="analysisSearchInput" placeholder="Search candidates...">
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" id="analysisScoreFilter">
                                            <option value="">All Scores</option>
                                            <option value="excellent">Excellent (85-100%)</option>
                                            <option value="good">Good (70-84%)</option>
                                            <option value="average">Average (50-69%)</option>
                                            <option value="poor">Poor (0-49%)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" id="analysisExperienceFilter">
                                            <option value="">All Experience</option>
                                            <option value="0-2">0-2 years</option>
                                            <option value="3-5">3-5 years</option>
                                            <option value="6-10">6-10 years</option>
                                            <option value="10+">10+ years</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" id="analysisSkillsFilter">
                                            <option value="">All Skills</option>
                                            <option value="JavaScript">JavaScript</option>
                                            <option value="Python">Python</option>
                                            <option value="React">React</option>
                                            <option value="Node.js">Node.js</option>
                                            <option value="Java">Java</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-danger w-100" id="runAIAnalysisBtn">
                                            <i class="bi bi-cpu me-1"></i>Run AI Analysis
                                        </button>
                                    </div>
                                </div>

                                <!-- Analysis Progress -->
                                <div id="analysisProgress" class="mb-4" style="display: none;">
                                    <div class="progress">
                                        <div id="analysisProgressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-danger" role="progressbar"></div>
                                    </div>
                                    <div id="analysisStatus" class="mt-2 text-center"></div>
                                </div>

                                <!-- CV List with AI Analysis Results -->
                                <div id="analysisCVList">
                                    <!-- CV list with AI scores will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Analysis Results Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-cpu me-2"></i>
                                    Detailed AI Analysis Results
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="atsResults">
                                    <!-- Detailed AI results will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Reports Tab -->
            <div class="tab-pane fade" id="ai-reports" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-dark text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-graph-up me-2"></i>
                                    HR Analytics & Reports
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card text-center border-primary">
                                            <div class="card-body">
                                                <h3 class="text-primary" id="totalCandidatesCount">0</h3>
                                                <p class="mb-0">Total Candidates</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-success">
                                            <div class="card-body">
                                                <h3 class="text-success" id="qualifiedCandidatesCount">0</h3>
                                                <p class="mb-0">Qualified (>80%)</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-warning">
                                            <div class="card-body">
                                                <h3 class="text-warning" id="averageATSScore">0</h3>
                                                <p class="mb-0">Avg AI Score</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-info">
                                            <div class="card-body">
                                                <h3 class="text-info" id="activeJobsCount">0</h3>
                                                <p class="mb-0">Active Jobs</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <button class="btn btn-primary w-100 mb-3" id="generateDetailedReport">
                                            <i class="bi bi-file-earmark-pdf me-2"></i>Generate Detailed Report
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button class="btn btn-success w-100 mb-3" id="exportCandidateData">
                                            <i class="bi bi-file-earmark-excel me-2"></i>Export Candidate Data
                                        </button>
                                    </div>
                                </div>

                                <div id="hrReportsContent">
                                    <div id="noReportsMessage" class="text-center p-4">
                                        <i class="bi bi-graph-up display-4 text-muted mb-3"></i>
                                        <h6>No AI Analysis Reports Available</h6>
                                        <p class="text-muted">Run AI Analysis from the AI Analysis tab to generate automated reports</p>
                                        <button class="btn btn-primary" onclick="document.getElementById('ai-analysis-tab').click()">
                                            <i class="bi bi-cpu me-2"></i>Go to AI Analysis
                                        </button>
                                    </div>

                                    <div id="analysisReports" style="display: none;">
                                        <!-- Job Description Summary -->
                                        <div class="card mb-4">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0"><i class="bi bi-briefcase me-2"></i>Job Requirements Analysis</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="jobDescriptionSummary"></div>
                                            </div>
                                        </div>

                                        <!-- Top Candidates -->
                                        <div class="card mb-4">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0"><i class="bi bi-trophy me-2"></i>Top Matching Candidates</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="topCandidates"></div>
                                            </div>
                                        </div>

                                        <!-- Skills Analysis -->
                                        <div class="card mb-4">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0"><i class="bi bi-gear me-2"></i>Skills Gap Analysis</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="skillsGapAnalysis"></div>
                                            </div>
                                        </div>

                                        <!-- Detailed Results Table -->
                                        <div class="card">
                                            <div class="card-header bg-dark text-white">
                                                <h6 class="mb-0"><i class="bi bi-table me-2"></i>Detailed Analysis Results</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="detailedResultsTable"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- AI API Setup Modal -->
    <div class="modal fade" id="apiSetupModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-google me-2"></i>
                        AI API Setup Guide
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-2"></i>Quick Setup (5 minutes)</h6>
                                <p class="mb-0">Follow these steps to get your free AI API key and enable AI-powered resume analysis.</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">📋 Step-by-Step Guide:</h6>
                            <ol class="list-group list-group-numbered">
                                <li class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">Visit Google AI Studio</div>
                                        Go to <a href="https://makersuite.google.com/app/apikey" target="_blank">makersuite.google.com</a>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">1</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">Sign in with Google</div>
                                        Use your Google account to access AI Studio
                                    </div>
                                    <span class="badge bg-primary rounded-pill">2</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">Create API Key</div>
                                        Click "Create API Key" button
                                    </div>
                                    <span class="badge bg-primary rounded-pill">3</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">Copy & Paste</div>
                                        Copy the API key and paste it above
                                    </div>
                                    <span class="badge bg-primary rounded-pill">4</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">Test Connection</div>
                                        Click "Test Connection" to verify
                                    </div>
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">🚀 What You'll Get:</h6>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <strong>Free API Access</strong><br>
                                    <small class="text-muted">1,500 requests per day at no cost</small>
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-robot text-primary me-2"></i>
                                    <strong>AI-Powered Analysis</strong><br>
                                    <small class="text-muted">Advanced resume scoring and insights</small>
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-lightning text-warning me-2"></i>
                                    <strong>Automated Workflow</strong><br>
                                    <small class="text-muted">Google Drive sync + AI analysis</small>
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-graph-up text-info me-2"></i>
                                    <strong>Detailed Reports</strong><br>
                                    <small class="text-muted">Skills matching and recommendations</small>
                                </li>
                            </ul>


                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="bi bi-shield-check text-success me-2"></i>
                                        Security & Privacy
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li>Your API key is stored locally in your browser only</li>
                                        <li>No data is sent to external servers except Google's AI API</li>
                                        <li>Resume data is processed securely through Google's infrastructure</li>
                                        <li>You can revoke API access anytime from Google AI Studio</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://makersuite.google.com/app/apikey" target="_blank" class="btn btn-primary">
                        <i class="bi bi-box-arrow-up-right me-2"></i>
                        Get API Key Now
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- HR API Service for Backend Integration -->
    <script src="hr-api-service.js"></script>
    <script src="database-service.js"></script>
    <script src="hr-dashboard.js"></script>

    <!-- Header Navigation Functions -->
    <script>
        // Header Quick Navigation Functions
        function connectDatabase() {
            // Navigate to Database Connection tab
            document.getElementById('database-connection-tab').click();
            showNotification('Navigate to Database Connection tab to configure database settings', 'info');
        }

        function connectGoogleDrive() {
            // Navigate to Google Drive tab
            document.getElementById('google-drive-tab').click();
            showNotification('Navigate to Google Drive tab to configure Google Drive connection', 'info');
        }

        function manageCVs() {
            // Navigate to CV Management tab
            document.getElementById('cv-management-tab').click();
            showNotification('Navigate to CV Management tab to upload and manage resumes', 'info');
        }

        function startAIAnalysis() {
            // Check if called from header or from AI Analysis tab
            const currentTab = document.querySelector('.nav-link.active');
            if (!currentTab || currentTab.id !== 'ai-analysis-tab') {
                // Navigate to AI Analysis tab
                document.getElementById('ai-analysis-tab').click();
                showNotification('Navigate to AI Analysis tab to start resume analysis', 'info');
                return;
            }

            // Perform actual AI analysis
            performAIAnalysis();
        }

        async function performAIAnalysis() {
            const jobDescription = document.getElementById('customJobDescription').value;
            const resumeFiles = document.getElementById('resumeFileInput').files;

            if (!jobDescription.trim()) {
                showNotification('Please enter a job description for better AI analysis', 'warning');
                return;
            }

            if (resumeFiles.length === 0) {
                showNotification('Please select at least one resume file to analyze', 'warning');
                return;
            }

            // Show progress
            showAnalysisProgress();

            try {
                const results = [];

                for (let i = 0; i < resumeFiles.length; i++) {
                    const file = resumeFiles[i];
                    updateAnalysisProgress((i / resumeFiles.length) * 100, `Analyzing ${file.name}...`);

                    const result = await analyzeResumeFile(file, jobDescription);
                    results.push(result);

                    // Small delay for better UX
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                updateAnalysisProgress(100, 'Analysis completed!');
                displayAnalysisResults(results);

                setTimeout(() => {
                    hideAnalysisProgress();
                    showNotification(`Successfully analyzed ${results.length} resume(s)!`, 'success');
                }, 1000);

            } catch (error) {
                console.error('AI Analysis failed:', error);
                hideAnalysisProgress();
                showNotification('AI Analysis failed. Please try again.', 'danger');
            }
        }

        async function analyzeResumeFile(file, jobDescription) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('job_description', jobDescription);

            const response = await fetch('http://localhost:8080/api/analyze-resume', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        }

        function showAnalysisProgress() {
            document.getElementById('analysisProgress').style.display = 'block';
            document.getElementById('startAIAnalysisBtn').disabled = true;
        }

        function hideAnalysisProgress() {
            document.getElementById('analysisProgress').style.display = 'none';
            document.getElementById('startAIAnalysisBtn').disabled = false;
        }

        function updateAnalysisProgress(percentage, status) {
            document.getElementById('analysisProgressBar').style.width = percentage + '%';
            document.getElementById('analysisStatus').textContent = status;
        }

        function displayAnalysisResults(results) {
            // Create results container if it doesn't exist
            let resultsContainer = document.getElementById('analysisResults');
            if (!resultsContainer) {
                resultsContainer = document.createElement('div');
                resultsContainer.id = 'analysisResults';
                resultsContainer.className = 'mt-4';

                // Find the AI Analysis card body and append results
                const aiAnalysisCard = document.querySelector('#ai-analysis .card-body');
                if (aiAnalysisCard) {
                    aiAnalysisCard.appendChild(resultsContainer);
                }
            }

            let resultsHTML = '<h6><i class="bi bi-list-check me-2"></i>AI Analysis Results</h6><div class="row">';

            results.forEach((result, index) => {
                if (result.success) {
                    const analysis = result.analysis;
                    const atsScore = analysis.ats_score.total_score;
                    const matchScore = analysis.job_match.match_score;

                    const scoreClass = atsScore >= 80 ? 'text-success' : atsScore >= 60 ? 'text-warning' : 'text-danger';
                    const matchClass = matchScore >= 80 ? 'text-success' : matchScore >= 60 ? 'text-warning' : 'text-danger';

                    resultsHTML += `
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">${result.filename}</h6>
                                    <span class="badge bg-primary">${analysis.ats_score.grade}</span>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center mb-3">
                                        <div class="col-6">
                                            <div class="stat-mini">
                                                <h5 class="${scoreClass}">${atsScore}%</h5>
                                                <small>ATS Score</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="stat-mini">
                                                <h5 class="${matchClass}">${matchScore}%</h5>
                                                <small>Job Match</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <strong>Contact:</strong> ${analysis.contact_info.email || 'Not found'}
                                    </div>
                                    <div class="mb-2">
                                        <strong>Skills Found:</strong>
                                        <div class="mt-1">
                                            ${Object.values(analysis.skills).flat().slice(0, 5).map(skill =>
                                                `<span class="badge bg-light text-dark me-1">${skill}</span>`
                                            ).join('')}
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <strong>Analysis Type:</strong>
                                        <span class="badge ${analysis.openai_powered ? 'bg-success' : 'bg-info'}">${analysis.analysis_type || 'Enhanced Local'}</span>
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewDetailedAnalysis(${index})">
                                        <i class="bi bi-eye"></i> View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    resultsHTML += `
                        <div class="col-md-6 mb-3">
                            <div class="card border-danger">
                                <div class="card-body">
                                    <h6 class="text-danger">Analysis Failed</h6>
                                    <p>Could not analyze ${result.filename}</p>
                                </div>
                            </div>
                        </div>
                    `;
                }
            });

            resultsHTML += '</div>';
            resultsContainer.innerHTML = resultsHTML;
        }

        function viewDetailedAnalysis(index) {
            showNotification('Detailed analysis view coming soon!', 'info');
        }

        // Bulk AI Analysis for HR Automation
        async function runBulkAIAnalysis() {
            const jobDescription = document.getElementById('customJobDescription').value;

            if (!jobDescription.trim()) {
                showNotification('Please enter job requirements for AI analysis', 'warning');
                return;
            }

            // Check if candidates are connected
            const totalCandidates = parseInt(document.getElementById('totalCandidatesCount').textContent) || 0;
            if (totalCandidates === 0) {
                showNotification('No candidates found. Please connect Google Drive or Database first.', 'warning');
                return;
            }

            // Show bulk analysis progress
            showBulkAnalysisProgress();

            try {
                // Simulate getting candidates from connected sources
                const candidates = await getCandidatesFromSources();

                if (candidates.length === 0) {
                    showNotification('No candidates found to analyze', 'warning');
                    hideBulkAnalysisProgress();
                    return;
                }

                showNotification(`Starting AI analysis for ${candidates.length} candidates...`, 'info');

                const analysisResults = [];

                // Analyze each candidate
                for (let i = 0; i < candidates.length; i++) {
                    const candidate = candidates[i];
                    updateBulkAnalysisProgress(
                        ((i + 1) / candidates.length) * 100,
                        `Analyzing ${candidate.name} (${i + 1}/${candidates.length})`
                    );

                    try {
                        const result = await analyzeCandidateResume(candidate, jobDescription);
                        analysisResults.push(result);

                        // Update candidate status
                        updateCandidateStatus(candidate.id, 'analyzed');

                    } catch (error) {
                        console.error(`Failed to analyze ${candidate.name}:`, error);
                        analysisResults.push({
                            candidate: candidate,
                            success: false,
                            error: error.message
                        });
                    }

                    // Small delay for better UX
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

                // Complete analysis
                updateBulkAnalysisProgress(100, 'Analysis completed! Generating reports...');

                // Store results for reports
                localStorage.setItem('aiAnalysisResults', JSON.stringify(analysisResults));
                localStorage.setItem('jobDescription', jobDescription);
                localStorage.setItem('analysisTimestamp', new Date().toISOString());

                // Update statistics
                updateAnalysisStatistics(analysisResults);

                // Show completion message
                setTimeout(() => {
                    hideBulkAnalysisProgress();
                    showNotification(`AI Analysis completed for ${analysisResults.length} candidates!`, 'success');

                    // Auto-navigate to AI Reports after 2 seconds
                    setTimeout(() => {
                        document.getElementById('ai-reports-tab').click();
                        showNotification('Navigating to AI Reports for detailed analysis...', 'info');
                    }, 2000);
                }, 1000);

            } catch (error) {
                console.error('Bulk AI Analysis failed:', error);
                hideBulkAnalysisProgress();
                showNotification('Bulk AI Analysis failed. Please try again.', 'danger');
            }
        }

        async function getCandidatesFromSources() {
            // Simulate getting candidates from Google Drive and Database
            const mockCandidates = [
                { id: 1, name: 'Arun Prakash', source: 'google_drive', file: 'arun_prakash_resume.pdf', email: '<EMAIL>' },
                { id: 2, name: 'Priya Lakshmi', source: 'database', file: 'priya_lakshmi_cv.docx', email: '<EMAIL>' },
                { id: 3, name: 'Karthik Raj', source: 'google_drive', file: 'karthik_raj_resume.pdf', email: '<EMAIL>' },
                { id: 4, name: 'Meera Devi', source: 'database', file: 'meera_devi_cv.pdf', email: '<EMAIL>' },
                { id: 5, name: 'Suresh Kumar', source: 'google_drive', file: 'suresh_kumar_resume.docx', email: '<EMAIL>' }
            ];

            // Update candidate counts
            const dbCount = mockCandidates.filter(c => c.source === 'database').length;
            const driveCount = mockCandidates.filter(c => c.source === 'google_drive').length;

            document.getElementById('dbCandidatesCount').textContent = dbCount;
            document.getElementById('driveCandidatesCount').textContent = driveCount;
            document.getElementById('totalCandidatesCount').textContent = `${mockCandidates.length} Candidates`;

            // Display candidates list
            displayCandidatesList(mockCandidates);

            return mockCandidates;
        }

        function displayCandidatesList(candidates) {
            const candidatesList = document.getElementById('candidatesList');

            let html = '<div class="table-responsive"><table class="table table-hover"><thead><tr>';
            html += '<th>Name</th><th>Source</th><th>File</th><th>Status</th><th>Actions</th></tr></thead><tbody>';

            candidates.forEach(candidate => {
                const sourceIcon = candidate.source === 'google_drive' ? 'bi-google text-success' : 'bi-database text-primary';
                html += `
                    <tr id="candidate-${candidate.id}">
                        <td><strong>${candidate.name}</strong><br><small class="text-muted">${candidate.email}</small></td>
                        <td><i class="bi ${sourceIcon} me-1"></i>${candidate.source.replace('_', ' ').toUpperCase()}</td>
                        <td><i class="bi bi-file-earmark-pdf text-danger me-1"></i>${candidate.file}</td>
                        <td><span class="badge bg-secondary" id="status-${candidate.id}">Ready</span></td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="previewCandidate(${candidate.id})">
                                <i class="bi bi-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            candidatesList.innerHTML = html;
        }

        async function analyzeCandidateResume(candidate, jobDescription) {
            // Simulate API call to analyze candidate resume
            // In real implementation, this would call the backend API

            const mockAnalysis = {
                candidate: candidate,
                success: true,
                analysis: {
                    ats_score: {
                        total_score: Math.floor(Math.random() * 40) + 60, // 60-100
                        grade: 'A',
                        breakdown: {
                            content: 25,
                            formatting: 20,
                            keywords: 15,
                            structure: 20
                        }
                    },
                    job_match: {
                        match_score: Math.floor(Math.random() * 50) + 50, // 50-100
                        matched_skills: ['React', 'Node.js', 'Python', 'AWS'],
                        missing_skills: ['Docker', 'Kubernetes']
                    },
                    contact_info: {
                        email: candidate.email,
                        phone: '+91-9876543210'
                    },
                    skills: {
                        technical: ['React', 'Node.js', 'Python'],
                        soft_skills: ['Communication', 'Leadership']
                    },
                    openai_powered: false,
                    analysis_type: 'Enhanced Local Analysis'
                }
            };

            return mockAnalysis;
        }

        function updateCandidateStatus(candidateId, status) {
            const statusElement = document.getElementById(`status-${candidateId}`);
            if (statusElement) {
                switch(status) {
                    case 'analyzing':
                        statusElement.className = 'badge bg-warning';
                        statusElement.textContent = 'Analyzing...';
                        break;
                    case 'analyzed':
                        statusElement.className = 'badge bg-success';
                        statusElement.textContent = 'Analyzed';
                        break;
                    case 'error':
                        statusElement.className = 'badge bg-danger';
                        statusElement.textContent = 'Error';
                        break;
                }
            }
        }

        function showBulkAnalysisProgress() {
            document.getElementById('analysisProgress').style.display = 'block';
            document.getElementById('runBulkAIAnalysis').disabled = true;
            document.getElementById('startAIAnalysisBtn').disabled = true;
        }

        function hideBulkAnalysisProgress() {
            document.getElementById('analysisProgress').style.display = 'none';
            document.getElementById('runBulkAIAnalysis').disabled = false;
            document.getElementById('startAIAnalysisBtn').disabled = false;
        }

        function updateBulkAnalysisProgress(percentage, status) {
            document.getElementById('analysisProgressBar').style.width = percentage + '%';
            document.getElementById('analysisStatus').textContent = status;
        }

        function updateAnalysisStatistics(results) {
            const analyzedCount = results.filter(r => r.success).length;
            const matchingCount = results.filter(r => r.success && r.analysis.job_match.match_score >= 70).length;

            document.getElementById('analyzedCount').textContent = analyzedCount;
            document.getElementById('matchingCount').textContent = matchingCount;
        }

        function refreshCandidates() {
            showNotification('Refreshing candidates from connected sources...', 'info');
            getCandidatesFromSources();
        }

        function previewCandidate(candidateId) {
            showNotification(`Preview for candidate ${candidateId} coming soon!`, 'info');
        }

        // AI Reports Functions
        function loadAIReports() {
            const analysisResults = localStorage.getItem('aiAnalysisResults');
            const jobDescription = localStorage.getItem('jobDescription');
            const analysisTimestamp = localStorage.getItem('analysisTimestamp');

            if (!analysisResults || !jobDescription) {
                document.getElementById('noReportsMessage').style.display = 'block';
                document.getElementById('analysisReports').style.display = 'none';
                return;
            }

            document.getElementById('noReportsMessage').style.display = 'none';
            document.getElementById('analysisReports').style.display = 'block';

            const results = JSON.parse(analysisResults);

            // Update statistics
            updateReportStatistics(results);

            // Display job description summary
            displayJobDescriptionSummary(jobDescription, analysisTimestamp);

            // Display top candidates
            displayTopCandidates(results);

            // Display skills gap analysis
            displaySkillsGapAnalysis(results);

            // Display detailed results table
            displayDetailedResultsTable(results);
        }

        function updateReportStatistics(results) {
            const totalCandidates = results.length;
            const qualifiedCandidates = results.filter(r => r.success && r.analysis.job_match.match_score >= 80).length;
            const averageScore = results.reduce((sum, r) => sum + (r.success ? r.analysis.ats_score.total_score : 0), 0) / totalCandidates;

            document.getElementById('totalCandidatesCount').textContent = totalCandidates;
            document.getElementById('qualifiedCandidatesCount').textContent = qualifiedCandidates;
            document.getElementById('averageATSScore').textContent = Math.round(averageScore) + '%';
            document.getElementById('activeJobsCount').textContent = '1';
        }

        function displayJobDescriptionSummary(jobDescription, timestamp) {
            const summaryDiv = document.getElementById('jobDescriptionSummary');
            const date = new Date(timestamp).toLocaleString();

            summaryDiv.innerHTML = `
                <div class="row">
                    <div class="col-md-8">
                        <h6>Job Requirements:</h6>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0">${jobDescription.substring(0, 300)}${jobDescription.length > 300 ? '...' : ''}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6>Analysis Details:</h6>
                        <ul class="list-unstyled">
                            <li><strong>Date:</strong> ${date}</li>
                            <li><strong>Type:</strong> Automated AI Analysis</li>
                            <li><strong>Status:</strong> <span class="badge bg-success">Completed</span></li>
                        </ul>
                    </div>
                </div>
            `;
        }

        function displayTopCandidates(results) {
            const topCandidatesDiv = document.getElementById('topCandidates');

            // Sort by job match score
            const sortedResults = results
                .filter(r => r.success)
                .sort((a, b) => b.analysis.job_match.match_score - a.analysis.job_match.match_score)
                .slice(0, 5);

            let html = '<div class="row">';

            sortedResults.forEach((result, index) => {
                const candidate = result.candidate;
                const analysis = result.analysis;
                const matchScore = analysis.job_match.match_score;
                const atsScore = analysis.ats_score.total_score;

                const matchClass = matchScore >= 80 ? 'success' : matchScore >= 60 ? 'warning' : 'danger';
                const rankBadge = index === 0 ? 'bg-warning' : index === 1 ? 'bg-secondary' : 'bg-info';

                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card border-${matchClass}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0">${candidate.name}</h6>
                                    <span class="badge ${rankBadge}">#${index + 1}</span>
                                </div>
                                <p class="text-muted small mb-2">${candidate.email}</p>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h5 class="text-${matchClass} mb-0">${matchScore}%</h5>
                                            <small>Job Match</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-primary mb-0">${atsScore}%</h5>
                                        <small>ATS Score</small>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small><strong>Skills:</strong> ${analysis.skills.technical.slice(0, 3).join(', ')}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            topCandidatesDiv.innerHTML = html;
        }

        function displaySkillsGapAnalysis(results) {
            const skillsDiv = document.getElementById('skillsGapAnalysis');

            // Aggregate skills data
            const allSkills = new Set();
            const skillCounts = {};

            results.forEach(result => {
                if (result.success) {
                    Object.values(result.analysis.skills).flat().forEach(skill => {
                        allSkills.add(skill);
                        skillCounts[skill] = (skillCounts[skill] || 0) + 1;
                    });
                }
            });

            // Sort skills by frequency
            const sortedSkills = Object.entries(skillCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Most Common Skills</h6>
                        <div class="skills-chart">
            `;

            sortedSkills.forEach(([skill, count]) => {
                const percentage = (count / results.length) * 100;
                html += `
                    <div class="mb-2">
                        <div class="d-flex justify-content-between">
                            <span>${skill}</span>
                            <span>${count}/${results.length} candidates</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-info" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                `;
            });

            html += `
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Skills Gap Recommendations</h6>
                        <div class="alert alert-info">
                            <ul class="mb-0">
                                <li>Focus on candidates with React.js and Node.js experience</li>
                                <li>Consider training programs for cloud platforms</li>
                                <li>Prioritize candidates with full-stack experience</li>
                                <li>Look for candidates with agile methodology background</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;

            skillsDiv.innerHTML = html;
        }

        function displayDetailedResultsTable(results) {
            const tableDiv = document.getElementById('detailedResultsTable');

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Rank</th>
                                <th>Candidate</th>
                                <th>Source</th>
                                <th>Job Match</th>
                                <th>ATS Score</th>
                                <th>Grade</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            // Sort by job match score
            const sortedResults = results
                .filter(r => r.success)
                .sort((a, b) => b.analysis.job_match.match_score - a.analysis.job_match.match_score);

            sortedResults.forEach((result, index) => {
                const candidate = result.candidate;
                const analysis = result.analysis;
                const matchScore = analysis.job_match.match_score;
                const atsScore = analysis.ats_score.total_score;

                const matchClass = matchScore >= 80 ? 'success' : matchScore >= 60 ? 'warning' : 'danger';
                const sourceIcon = candidate.source === 'google_drive' ? 'bi-google text-success' : 'bi-database text-primary';

                html += `
                    <tr>
                        <td><span class="badge bg-secondary">#${index + 1}</span></td>
                        <td>
                            <strong>${candidate.name}</strong><br>
                            <small class="text-muted">${candidate.email}</small>
                        </td>
                        <td><i class="bi ${sourceIcon} me-1"></i>${candidate.source.replace('_', ' ')}</td>
                        <td><span class="badge bg-${matchClass}">${matchScore}%</span></td>
                        <td>${atsScore}%</td>
                        <td><span class="badge bg-primary">${analysis.ats_score.grade}</span></td>
                        <td><span class="badge bg-success">Analyzed</span></td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewCandidateDetails(${candidate.id})">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="shortlistCandidate(${candidate.id})">
                                <i class="bi bi-check"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            tableDiv.innerHTML = html;
        }

        function viewCandidateDetails(candidateId) {
            showNotification(`Viewing detailed analysis for candidate ${candidateId}`, 'info');
        }

        function shortlistCandidate(candidateId) {
            showNotification(`Candidate ${candidateId} added to shortlist!`, 'success');
        }

        // Utility function for notifications
        function showNotification(message, type = 'info') {
            // Create a simple notification
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="bi bi-info-circle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 4 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 4000);
        }

        // Update header status indicators
        function updateHeaderStatus() {
            // Update database status
            const dbStatus = document.getElementById('dbStatus');
            if (dbStatus) {
                dbStatus.textContent = 'Not Connected';
                dbStatus.className = 'badge bg-warning';
            }

            // Update Google Drive status
            const driveStatus = document.getElementById('driveStatus');
            if (driveStatus) {
                driveStatus.textContent = 'Not Connected';
                driveStatus.className = 'badge bg-warning';
            }

            // Update CV count
            const totalCVs = document.getElementById('totalCVs');
            if (totalCVs) {
                totalCVs.textContent = '0';
                totalCVs.className = 'badge bg-info';
            }
        }

        // Initialize header functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 CubeAI HR Dashboard Header Navigation Loaded');
            updateHeaderStatus();

            // Add hover effects to header buttons
            const headerButtons = document.querySelectorAll('.connection-panel .btn');
            headerButtons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add tab change listeners
            const aiReportsTab = document.getElementById('ai-reports-tab');
            if (aiReportsTab) {
                aiReportsTab.addEventListener('click', function() {
                    setTimeout(() => {
                        loadAIReports();
                    }, 100);
                });
            }

            // Load candidates when AI Analysis tab is opened
            const aiAnalysisTab = document.getElementById('ai-analysis-tab');
            if (aiAnalysisTab) {
                aiAnalysisTab.addEventListener('click', function() {
                    setTimeout(() => {
                        getCandidatesFromSources();
                    }, 100);
                });
            }
        });
    </script>
</body>
</html>
