#!/usr/bin/env python3
"""
Simple test server to verify <PERSON><PERSON><PERSON> is working
"""

from flask import Flask, jsonify, send_from_directory
from flask_cors import CORS
import os

app = Flask(__name__, static_folder='../frontend', static_url_path='')
CORS(app)

@app.route('/')
def index():
    """Serve the main page"""
    return send_from_directory('../frontend', 'hr-dashboard.html')

@app.route('/hr-dashboard.html')
def dashboard():
    """Serve the HR dashboard"""
    return send_from_directory('../frontend', 'hr-dashboard.html')

@app.route('/api/health')
def health():
    """Simple health check"""
    return jsonify({
        'status': 'healthy',
        'message': 'CubeAI HR Dashboard Test Server',
        'server': 'Flask Test Server',
        'port': 5000
    })

@app.route('/test')
def test():
    """Test endpoint"""
    return """
    <html>
    <head><title>CubeAI Test Server</title></head>
    <body>
        <h1>🚀 CubeAI HR Dashboard Test Server</h1>
        <p>✅ Flask server is working!</p>
        <p>✅ Connection successful!</p>
        <p><a href="/hr-dashboard.html">Go to HR Dashboard</a></p>
        <p><a href="/api/health">Check API Health</a></p>
    </body>
    </html>
    """

if __name__ == '__main__':
    print("🚀 Starting CubeAI Test Server...")
    print("🌐 Access: http://localhost:8080")
    print("📊 Dashboard: http://localhost:8080/hr-dashboard.html")
    print("🔍 Test: http://localhost:8080/test")
    print("=" * 50)
    
    try:
        app.run(
            host='127.0.0.1',
            port=8080,
            debug=True,
            use_reloader=False  # Disable reloader to avoid issues
        )
    except Exception as e:
        print(f"❌ Server failed to start: {e}")
        print("🔧 Try running as administrator or check firewall settings")
