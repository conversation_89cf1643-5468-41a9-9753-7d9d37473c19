ARUN PRAKASH
Senior Full Stack Developer
Email: <EMAIL>
Phone: +91-9876543210
Location: Chennai, Tamil Nadu

PROFESSIONAL SUMMARY
Experienced Full Stack Developer with 5+ years of expertise in building scalable web applications using modern technologies. Proven track record in React.js, Node.js, Python, and cloud platforms. Strong problem-solving skills and experience in agile development methodologies.

TECHNICAL SKILLS
Frontend: React.js, Angular, Vue.js, HTML5, CSS3, JavaScript (ES6+), TypeScript
Backend: Node.js, Python, Django, Flask, Express.js, RESTful APIs
Databases: MongoDB, PostgreSQL, MySQL, Redis
Cloud & DevOps: AWS (EC2, S3, Lambda), Docker, Kubernetes, CI/CD
Tools: Git, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ra

PROFESSIONAL EXPERIENCE

Senior Full Stack Developer | TechCorp Solutions | 2021 - Present
• Led development of 3 major web applications serving 10,000+ users
• Implemented microservices architecture reducing system downtime by 40%
• Mentored junior developers and conducted code reviews
• Collaborated with cross-functional teams using Agile methodologies
• Optimized database queries improving application performance by 35%

Full Stack Developer | InnovateTech | 2019 - 2021
• Developed responsive web applications using React.js and Node.js
• Built RESTful APIs handling 1M+ requests per day
• Integrated third-party services and payment gateways
• Implemented automated testing reducing bugs by 50%
• Participated in sprint planning and daily standups

Junior Developer | StartupHub | 2018 - 2019
• Assisted in frontend development using HTML, CSS, and JavaScript
• Learned modern frameworks and contributed to team projects
• Fixed bugs and implemented minor features
• Participated in code reviews and team meetings

EDUCATION
Bachelor of Technology in Computer Science
Anna University, Chennai | 2014 - 2018
CGPA: 8.2/10

PROJECTS
E-Commerce Platform (2022)
• Built full-stack e-commerce solution with React.js and Node.js
• Implemented secure payment processing and user authentication
• Deployed on AWS with auto-scaling capabilities

Task Management System (2021)
• Developed project management tool with real-time collaboration
• Used Socket.io for real-time updates and MongoDB for data storage
• Implemented role-based access control

CERTIFICATIONS
• AWS Certified Solutions Architect - Associate (2022)
• MongoDB Certified Developer (2021)
• React.js Certification - Meta (2020)

ACHIEVEMENTS
• Employee of the Month - TechCorp Solutions (3 times)
• Led team that won company hackathon (2022)
• Speaker at Chennai Tech Meetup on "Modern Web Development"

LANGUAGES
• English (Fluent)
• Tamil (Native)
• Hindi (Conversational)
