#!/usr/bin/env python3
"""
Test AI Resume Analysis
Demonstrates how the AI analyzes resumes
"""

import requests
import json
import os

def test_ai_resume_analysis():
    """Test the AI resume analysis endpoint"""
    
    # API endpoint
    url = "http://localhost:8080/api/analyze-resume"
    
    # Job description for matching
    job_description = """
    Looking for a Senior Full Stack Developer with:
    - 3+ years experience in React.js and Node.js
    - Experience with cloud platforms (AWS preferred)
    - Database management skills (MongoDB, PostgreSQL)
    - Agile development methodology experience
    - Strong problem-solving and communication skills
    - Experience with microservices architecture
    """
    
    # Sample resume file
    resume_file = "sample_resume.txt"
    
    if not os.path.exists(resume_file):
        print(f"❌ Resume file {resume_file} not found")
        return
    
    try:
        print("🚀 Testing AI Resume Analysis...")
        print("=" * 60)
        
        # Prepare the request
        with open(resume_file, 'rb') as f:
            files = {'file': (resume_file, f, 'text/plain')}
            data = {'job_description': job_description}
            
            # Make the API request
            print("📤 Sending resume for AI analysis...")
            response = requests.post(url, files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ AI Analysis Completed Successfully!")
            print("=" * 60)
            
            # Display results
            if result.get('success'):
                analysis = result['analysis']
                
                print(f"📄 Resume: {result['filename']}")
                print(f"📊 Analysis Type: {analysis.get('analysis_type', 'Standard')}")
                print(f"🤖 OpenAI Powered: {analysis.get('openai_powered', False)}")
                print()
                
                # ATS Score
                if 'ats_score' in analysis:
                    ats = analysis['ats_score']
                    print(f"🎯 ATS SCORE: {ats['total_score']}% (Grade: {ats['grade']})")
                    print("   Breakdown:")
                    for category, score in ats['breakdown'].items():
                        print(f"   - {category.title()}: {score}%")
                    print()
                
                # Job Match
                if 'job_match' in analysis:
                    match = analysis['job_match']
                    print(f"🎯 JOB MATCH: {match['match_score']}%")
                    if match['matched_skills']:
                        print(f"   ✅ Matched Skills: {', '.join(match['matched_skills'][:5])}")
                    if match['missing_skills']:
                        print(f"   ❌ Missing Skills: {', '.join(match['missing_skills'][:5])}")
                    print()
                
                # Contact Info
                if 'contact_info' in analysis:
                    contact = analysis['contact_info']
                    print("📞 CONTACT INFORMATION:")
                    print(f"   Email: {contact.get('email', 'Not found')}")
                    print(f"   Phone: {contact.get('phone', 'Not found')}")
                    print()
                
                # Skills Found
                if 'skills' in analysis:
                    skills = analysis['skills']
                    print("🛠️ SKILLS ANALYSIS:")
                    for category, skill_list in skills.items():
                        if skill_list:
                            print(f"   {category.title()}: {', '.join(skill_list[:5])}")
                    print()
                
                # AI Insights (if available)
                if 'ai_insights' in analysis:
                    insights = analysis['ai_insights']
                    print("🧠 AI INSIGHTS:")
                    print(f"   Overall Score: {insights.get('overall_score', 'N/A')}%")
                    if 'content_quality' in insights:
                        quality = insights['content_quality']
                        print(f"   Content Quality: {quality.get('score', 'N/A')}%")
                        if quality.get('strengths'):
                            print(f"   Strengths: {', '.join(quality['strengths'][:3])}")
                    print()
                
                # Recommendations
                if 'optimization_suggestions' in analysis:
                    suggestions = analysis['optimization_suggestions']
                    print("💡 OPTIMIZATION SUGGESTIONS:")
                    for i, suggestion in enumerate(suggestions[:5], 1):
                        print(f"   {i}. {suggestion}")
                    print()
                
                # Overall Assessment
                if 'overall_assessment' in analysis:
                    print("📋 OVERALL ASSESSMENT:")
                    print(f"   {analysis['overall_assessment']}")
                
            else:
                print("❌ Analysis failed")
                
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure the server is running on localhost:8080")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🤖 CubeAI Resume Analysis Test")
    print("Testing AI-powered resume analysis capabilities")
    print()
    test_ai_resume_analysis()
