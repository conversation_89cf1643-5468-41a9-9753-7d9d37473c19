# Cube AI Solutions - Flask Backend Configuration
# Copy this file to .env and update with your actual values

# Flask Configuration
SECRET_KEY=cube-ai-hr-secret-key-2024
FLASK_ENV=development
FLASK_DEBUG=True

# Database Configuration
DATABASE_URL=sqlite:///hr_automation.db

# JWT Configuration
JWT_SECRET_KEY=jwt-secret-string-change-in-production

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Google Drive Configuration
GOOGLE_CREDENTIALS_PATH=credentials.json

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Application Settings
APP_NAME=Cube AI HR Automation
APP_VERSION=2.0.0
