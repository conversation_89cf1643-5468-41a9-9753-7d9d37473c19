#!/usr/bin/env python3
"""
Cube AI Solutions - Flask Management CLI
Command-line interface for managing the HR automation system

Author: Cube AI Solutions
Version: 2.0.0
"""

import os
import sys
import click
import json
from datetime import datetime
from flask.cli import with_appcontext

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, JobPosting, Candidate, AnalysisResult
from tasks import celery_app

@click.group()
def cli():
    """Cube AI HR Automation Management CLI"""
    pass

@cli.command()
@with_appcontext
def init_db():
    """Initialize the database with tables and sample data"""
    click.echo("🔄 Initializing database...")
    
    # Create all tables
    db.create_all()
    
    # Create sample admin user
    admin_user = User.query.filter_by(email='<EMAIL>').first()
    if not admin_user:
        admin_user = User(
            email='<EMAIL>',
            name='Admin User',
            role='admin',
            password_hash='hashed_password_here'  # In production, use proper hashing
        )
        db.session.add(admin_user)
    
    # Create sample job postings
    sample_jobs = [
        {
            'title': 'Senior Software Engineer',
            'description': 'We are looking for a Senior Software Engineer with 5+ years of experience in Python, React, and cloud technologies.',
            'requirements': 'Python, React, AWS, Docker, Kubernetes',
            'department': 'Engineering',
            'location': 'Chennai, Tamil Nadu',
            'salary_range': '₹15-25 LPA'
        },
        {
            'title': 'Data Scientist',
            'description': 'Join our AI team as a Data Scientist to work on cutting-edge machine learning projects.',
            'requirements': 'Python, Machine Learning, TensorFlow, Statistics',
            'department': 'AI/ML',
            'location': 'Bangalore, Karnataka',
            'salary_range': '₹20-30 LPA'
        },
        {
            'title': 'Product Manager',
            'description': 'Lead product development and strategy for our AI-powered recruitment platform.',
            'requirements': 'Product Management, Agile, Analytics, Leadership',
            'department': 'Product',
            'location': 'Mumbai, Maharashtra',
            'salary_range': '₹25-35 LPA'
        }
    ]
    
    for job_data in sample_jobs:
        existing_job = JobPosting.query.filter_by(title=job_data['title']).first()
        if not existing_job:
            job = JobPosting(
                title=job_data['title'],
                description=job_data['description'],
                requirements=job_data['requirements'],
                department=job_data['department'],
                location=job_data['location'],
                salary_range=job_data['salary_range'],
                created_by=admin_user.id
            )
            db.session.add(job)
    
    # Create sample candidates with Tamil names
    sample_candidates = [
        {
            'name': 'Arun Prakash',
            'email': '<EMAIL>',
            'phone': '+91-9876543210',
            'experience_years': 5,
            'education': 'B.Tech Computer Science, Anna University',
            'skills': ['Python', 'React', 'AWS', 'Docker']
        },
        {
            'name': 'Keerthana Suresh',
            'email': '<EMAIL>',
            'phone': '+91-9876543211',
            'experience_years': 3,
            'education': 'M.Tech Data Science, IIT Madras',
            'skills': ['Python', 'Machine Learning', 'TensorFlow', 'Statistics']
        },
        {
            'name': 'Rajesh Kumar',
            'email': '<EMAIL>',
            'phone': '+91-9876543212',
            'experience_years': 7,
            'education': 'MBA + B.Tech, IIM Bangalore',
            'skills': ['Product Management', 'Agile', 'Analytics', 'Leadership']
        },
        {
            'name': 'Priya Raman',
            'email': '<EMAIL>',
            'phone': '+91-9876543213',
            'experience_years': 4,
            'education': 'B.Tech IT, VIT University',
            'skills': ['Java', 'Spring Boot', 'Microservices', 'Kubernetes']
        },
        {
            'name': 'Vikram Chandran',
            'email': '<EMAIL>',
            'phone': '+91-9876543214',
            'experience_years': 6,
            'education': 'M.Sc Computer Science, University of Madras',
            'skills': ['DevOps', 'AWS', 'Terraform', 'Jenkins']
        }
    ]
    
    for candidate_data in sample_candidates:
        existing_candidate = Candidate.query.filter_by(email=candidate_data['email']).first()
        if not existing_candidate:
            candidate = Candidate(
                name=candidate_data['name'],
                email=candidate_data['email'],
                phone=candidate_data['phone'],
                experience_years=candidate_data['experience_years'],
                education=candidate_data['education'],
                skills=json.dumps(candidate_data['skills'])
            )
            db.session.add(candidate)
    
    db.session.commit()
    click.echo("✅ Database initialized successfully!")

@cli.command()
@click.option('--host', default='0.0.0.0', help='Host to bind to')
@click.option('--port', default=5000, help='Port to bind to')
@click.option('--debug', is_flag=True, help='Enable debug mode')
def runserver(host, port, debug):
    """Run the Flask development server"""
    click.echo(f"🚀 Starting Flask server on {host}:{port}")
    app.run(host=host, port=port, debug=debug)

@cli.command()
def start_worker():
    """Start Celery worker for background tasks"""
    click.echo("🔄 Starting Celery worker...")
    celery_app.worker_main(['worker', '--loglevel=info'])

@cli.command()
def start_beat():
    """Start Celery beat scheduler for periodic tasks"""
    click.echo("⏰ Starting Celery beat scheduler...")
    celery_app.start(['beat', '--loglevel=info'])

@cli.command()
@click.argument('resume_path')
@click.argument('job_description')
def analyze_resume(resume_path, job_description):
    """Analyze a single resume file"""
    if not os.path.exists(resume_path):
        click.echo(f"❌ Resume file not found: {resume_path}")
        return
    
    click.echo(f"🔄 Analyzing resume: {resume_path}")
    
    # Queue the analysis task
    from tasks import analyze_resume_async
    task = analyze_resume_async.delay(resume_path, job_description)
    
    click.echo(f"✅ Analysis queued with task ID: {task.id}")
    click.echo("Use 'python manage.py task-status <task_id>' to check progress")

@cli.command()
@click.argument('directory_path')
@click.argument('job_description')
def batch_analyze(directory_path, job_description):
    """Analyze all resumes in a directory"""
    if not os.path.exists(directory_path):
        click.echo(f"❌ Directory not found: {directory_path}")
        return
    
    # Find all resume files
    resume_extensions = ['.pdf', '.doc', '.docx']
    resume_files = []
    
    for filename in os.listdir(directory_path):
        if any(filename.lower().endswith(ext) for ext in resume_extensions):
            resume_files.append(os.path.join(directory_path, filename))
    
    if not resume_files:
        click.echo("❌ No resume files found in directory")
        return
    
    click.echo(f"🔄 Found {len(resume_files)} resume files")
    
    # Queue batch analysis
    from tasks import batch_analyze_resumes
    task = batch_analyze_resumes.delay(resume_files, job_description)
    
    click.echo(f"✅ Batch analysis queued with task ID: {task.id}")

@cli.command()
@click.argument('task_id')
def task_status(task_id):
    """Check the status of a background task"""
    from tasks import get_task_status
    
    result = get_task_status(task_id)
    
    click.echo(f"Task ID: {result['task_id']}")
    click.echo(f"Status: {result['status']}")
    
    if result['result']:
        click.echo("Result:")
        click.echo(json.dumps(result['result'], indent=2))

@cli.command()
def list_candidates():
    """List all candidates in the database"""
    with app.app_context():
        candidates = Candidate.query.all()
        
        if not candidates:
            click.echo("No candidates found")
            return
        
        click.echo(f"Found {len(candidates)} candidates:")
        click.echo("-" * 80)
        
        for candidate in candidates:
            click.echo(f"ID: {candidate.id}")
            click.echo(f"Name: {candidate.name}")
            click.echo(f"Email: {candidate.email}")
            click.echo(f"Experience: {candidate.experience_years} years")
            click.echo(f"Status: {candidate.status}")
            click.echo(f"Created: {candidate.created_at}")
            click.echo("-" * 80)

@cli.command()
def list_jobs():
    """List all job postings"""
    with app.app_context():
        jobs = JobPosting.query.filter_by(is_active=True).all()
        
        if not jobs:
            click.echo("No active job postings found")
            return
        
        click.echo(f"Found {len(jobs)} active job postings:")
        click.echo("-" * 80)
        
        for job in jobs:
            click.echo(f"ID: {job.id}")
            click.echo(f"Title: {job.title}")
            click.echo(f"Department: {job.department}")
            click.echo(f"Location: {job.location}")
            click.echo(f"Salary: {job.salary_range}")
            click.echo(f"Created: {job.created_at}")
            click.echo("-" * 80)

@cli.command()
def generate_report():
    """Generate a recruitment report"""
    with app.app_context():
        # Get statistics
        total_candidates = Candidate.query.count()
        total_jobs = JobPosting.query.filter_by(is_active=True).count()
        total_analyses = AnalysisResult.query.count()
        
        # Generate report
        report = {
            'generated_at': datetime.now().isoformat(),
            'statistics': {
                'total_candidates': total_candidates,
                'active_jobs': total_jobs,
                'completed_analyses': total_analyses
            },
            'recent_candidates': [
                {
                    'name': c.name,
                    'email': c.email,
                    'status': c.status,
                    'created_at': c.created_at.isoformat()
                }
                for c in Candidate.query.order_by(Candidate.created_at.desc()).limit(5)
            ]
        }
        
        # Save report
        report_filename = f"recruitment_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        click.echo(f"✅ Report generated: {report_filename}")
        click.echo(json.dumps(report, indent=2))

@cli.command()
def health_check():
    """Check system health"""
    click.echo("🔍 Performing health check...")
    
    # Check database connection
    try:
        with app.app_context():
            db.session.execute('SELECT 1')
        click.echo("✅ Database: Connected")
    except Exception as e:
        click.echo(f"❌ Database: Error - {str(e)}")
    
    # Check Celery connection
    try:
        celery_app.control.inspect().stats()
        click.echo("✅ Celery: Connected")
    except Exception as e:
        click.echo(f"❌ Celery: Error - {str(e)}")
    
    # Check upload directory
    upload_dir = app.config.get('UPLOAD_FOLDER', 'uploads')
    if os.path.exists(upload_dir):
        click.echo(f"✅ Upload directory: {upload_dir}")
    else:
        click.echo(f"❌ Upload directory not found: {upload_dir}")
    
    click.echo("🏁 Health check completed")

if __name__ == '__main__':
    cli()
