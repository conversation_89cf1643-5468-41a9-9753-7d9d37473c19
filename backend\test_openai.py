#!/usr/bin/env python3
"""
Test OpenAI Integration for CubeAI Resume Analyzer
"""

import os
from openai import OpenAI

# Your OpenAI API key
api_key = "********************************************************************************************************************************************************************"

def test_openai_connection():
    """Test OpenAI API connection"""
    try:
        print("🔄 Testing OpenAI connection...")
        client = OpenAI(api_key=api_key)
        
        # Test with a simple completion
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say 'Hello from CubeAI Resume Analyzer!'"}
            ],
            max_tokens=50
        )
        
        print("✅ OpenAI connection successful!")
        print(f"📝 Response: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI connection failed: {str(e)}")
        return False

def test_resume_analysis():
    """Test resume analysis with OpenAI"""
    try:
        print("\n🔄 Testing resume analysis...")
        client = OpenAI(api_key=api_key)
        
        sample_resume = """
        John Doe
        Software Engineer
        Email: <EMAIL>
        Phone: ******-567-8900
        
        Experience:
        - 3 years of Python development
        - React.js frontend development
        - AWS cloud services
        - MongoDB database management
        
        Skills: Python, JavaScript, React, Node.js, AWS, MongoDB
        """
        
        job_description = """
        Looking for a Full Stack Developer with:
        - 2+ years Python experience
        - React.js knowledge
        - Cloud platform experience
        - Database management skills
        """
        
        prompt = f"""
        Analyze this resume against the job description and provide a JSON response:
        
        RESUME:
        {sample_resume}
        
        JOB DESCRIPTION:
        {job_description}
        
        Provide analysis in JSON format with overall_score (0-100), matched_skills, missing_skills, and recommendations.
        """
        
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are an expert HR analyst. Provide detailed resume analysis in JSON format."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=800,
            temperature=0.3
        )
        
        print("✅ Resume analysis successful!")
        print(f"📊 Analysis: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ Resume analysis failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 CubeAI OpenAI Integration Test")
    print("=" * 50)
    
    # Test basic connection
    connection_ok = test_openai_connection()
    
    if connection_ok:
        # Test resume analysis
        test_resume_analysis()
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")
