#!/usr/bin/env python3
"""
Cube AI Solutions - Automation Startup Script
Easy startup script for the Flask-based HR automation system

Author: Cube AI Solutions
Version: 2.0.0
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

def print_banner():
    """Print startup banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🔮 Cube AI Solutions - HR Automation System          ║
    ║                                                              ║
    ║        Flask-based AI-Powered Resume Screening              ║
    ║        Version 2.0.0                                        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'flask',
        'flask_cors',
        'flask_restful',
        'flask_sqlalchemy',
        'celery',
        'redis'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("📦 Installing missing packages...")
        
        # Install missing packages
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            '-r', 'config/requirements.txt'
        ], check=True)
        
        print("✅ Dependencies installed successfully!")
    else:
        print("✅ All dependencies are installed!")

def setup_environment():
    """Setup environment variables and directories"""
    print("🔧 Setting up environment...")
    
    # Create necessary directories
    directories = ['uploads', 'logs', 'reports']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 Created directory: {directory}")
    
    # Check for .env file
    if not os.path.exists('.env'):
        if os.path.exists('.env.flask'):
            print("📋 Copying .env.flask to .env")
            with open('.env.flask', 'r') as src, open('.env', 'w') as dst:
                dst.write(src.read())
        else:
            print("⚠️  No .env file found. Please create one based on .env.example")
    
    print("✅ Environment setup completed!")

def initialize_database():
    """Initialize the database"""
    print("🗄️  Initializing database...")
    
    try:
        subprocess.run([sys.executable, 'manage.py', 'init-db'], check=True)
        print("✅ Database initialized successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    
    return True

def start_redis():
    """Start Redis server if not running"""
    print("🔴 Checking Redis server...")
    
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis server is running")
        return True
    except:
        print("⚠️  Redis server not running. Please start Redis manually:")
        print("   - Windows: Download and run Redis from https://redis.io/download")
        print("   - Linux/Mac: sudo service redis-server start")
        return False

def start_flask_server():
    """Start the Flask development server"""
    print("🚀 Starting Flask server...")
    
    env = os.environ.copy()
    env['FLASK_APP'] = 'app.py'
    env['FLASK_ENV'] = 'development'
    
    process = subprocess.Popen([
        sys.executable, 'app.py'
    ], env=env)
    
    return process

def start_celery_worker():
    """Start Celery worker"""
    print("👷 Starting Celery worker...")
    
    process = subprocess.Popen([
        sys.executable, 'manage.py', 'start-worker'
    ])
    
    return process

def start_celery_beat():
    """Start Celery beat scheduler"""
    print("⏰ Starting Celery beat scheduler...")
    
    process = subprocess.Popen([
        sys.executable, 'manage.py', 'start-beat'
    ])
    
    return process

def health_check():
    """Perform system health check"""
    print("🏥 Performing health check...")
    
    try:
        subprocess.run([sys.executable, 'manage.py', 'health-check'], check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def signal_handler(sig, frame):
    """Handle shutdown signals"""
    print("\n🛑 Shutting down automation system...")
    
    # Terminate all child processes
    for process in active_processes:
        if process.poll() is None:  # Process is still running
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
    
    print("✅ Automation system stopped")
    sys.exit(0)

# Global list to track active processes
active_processes = []

def main():
    """Main startup function"""
    print_banner()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Check dependencies
        check_dependencies()
        
        # Setup environment
        setup_environment()
        
        # Initialize database
        if not initialize_database():
            print("❌ Failed to initialize database. Exiting...")
            return
        
        # Check Redis
        redis_running = start_redis()
        
        # Start Flask server
        flask_process = start_flask_server()
        active_processes.append(flask_process)
        
        # Start Celery components if Redis is available
        if redis_running:
            celery_worker = start_celery_worker()
            active_processes.append(celery_worker)
            
            celery_beat = start_celery_beat()
            active_processes.append(celery_beat)
        else:
            print("⚠️  Celery services not started due to Redis unavailability")
        
        # Wait a moment for services to start
        time.sleep(3)
        
        # Perform health check
        if health_check():
            print("\n🎉 Automation system started successfully!")
            print("\n📋 System Information:")
            print("   🌐 Flask Server: http://localhost:5000")
            print("   📊 API Health: http://localhost:5000/api/health")
            print("   📚 API Docs: Available at /api endpoints")
            
            if redis_running:
                print("   👷 Celery Worker: Running")
                print("   ⏰ Celery Beat: Running")
            
            print("\n🔧 Management Commands:")
            print("   python manage.py list-candidates")
            print("   python manage.py list-jobs")
            print("   python manage.py analyze-resume <path> <job_desc>")
            print("   python manage.py generate-report")
            
            print("\n⚠️  Press Ctrl+C to stop the automation system")
            
            # Keep the main process running
            try:
                while True:
                    time.sleep(1)
                    
                    # Check if any process has died
                    for i, process in enumerate(active_processes):
                        if process.poll() is not None:
                            print(f"⚠️  Process {i} has stopped unexpectedly")
            
            except KeyboardInterrupt:
                signal_handler(signal.SIGINT, None)
        
        else:
            print("❌ Health check failed. Please check the logs.")
            signal_handler(signal.SIGTERM, None)
    
    except Exception as e:
        print(f"❌ Startup failed: {str(e)}")
        signal_handler(signal.SIGTERM, None)

if __name__ == '__main__':
    main()
