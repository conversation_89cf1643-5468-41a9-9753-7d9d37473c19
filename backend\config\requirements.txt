# Cube AI Solutions - HR Agent Dependencies
# Install with: pip install -r requirements.txt

# Flask Web Framework
flask==2.3.3
flask-cors==4.0.0
flask-restful==0.3.10
flask-sqlalchemy==3.0.5
flask-migrate==4.0.5
flask-jwt-extended==4.5.3

# OpenAI API
openai>=0.28.0

# Google Drive API
google-api-python-client>=2.0.0
google-auth>=2.0.0
google-auth-oauthlib>=0.5.0
google-auth-httplib2>=0.1.0

# PDF Processing
PyMuPDF>=1.23.0
pdfplumber>=0.9.0
python-docx>=0.8.11

# Machine Learning & Data Processing
numpy>=1.21.0
scikit-learn>=1.0.0
pandas>=1.5.0

# Database (sqlite3 is built into Python)

# Task Queue & Background Jobs
celery>=5.3.0
redis>=5.0.0

# Production Server
gunicorn>=21.0.0

# File Upload & Processing
python-multipart>=0.0.6
werkzeug>=2.3.0

# Environment & Configuration
python-dotenv>=1.0.0

# Utilities
requests>=2.28.0
